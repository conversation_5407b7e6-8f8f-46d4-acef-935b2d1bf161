"use client";
import React from "react";
import "@/components/heroSection/heroSection.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectFade, Autoplay, Navigation, Pagination } from "swiper/modules";
// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-fade";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { heroSlides, heroSectionContent } from "@/constant";

const HeroSection = () => {
  return (
    <Box className="hero-section-container">
      <section className="swiper-section-container">
        <Swiper
          modules={[EffectFade, Autoplay, Navigation, Pagination]}
          effect="fade"
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          navigation={true}
          pagination={{ clickable: true }}
          speed={2000}
          loop={true}
          className="swiper-container"
        >
          {heroSlides.map((slide) => (
            <SwiperSlide key={slide.id}>
              <div className="slide-image-container">
                <Image
                  src={slide.image}
                  alt="Hero Carousel"
                  fill
                  style={{ objectFit: "cover" }}
                  priority={slide.id === 1}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </section>

      <Box className="hero-section-content">
        <Typography className="hero-section-text">
          {heroSectionContent.mainText}
        </Typography>
      </Box>
    </Box>
  );
};

export default HeroSection;
