.hero-section-container {
    margin-bottom: 80px;

    .swiper-section-container {
        position: relative;
        width: 100%;
        height: 90vh;
        overflow: hidden;

        .swiper-container {
            width: 100%;
            height: 100%;

            .swiper-slide {
                opacity: 0;
                transition: opacity 2s ease-in-out;

                &-active {
                    opacity: 1;
                }
            }

            .swiper-button-next,
            .swiper-button-prev {
                color: white;

                &::after {
                    font-size: 24px;
                }
            }

            .swiper-pagination-bullet {
                background: white;
                opacity: 0.5;

                &-active {
                    opacity: 1;
                }
            }
        }

        .slide-image-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
    }

    .hero-section-content {
        margin-top: 80px;
        padding: 0 70px;

        .hero-section-text {
            font-family: Poppins;
            font-weight: 400;
            font-size: 24px;

            line-height: 34px;
            letter-spacing: 0%;
            text-align: justify;
            color: #FFFFFF;
        }
    }
}

// media queries



@media (max-width: 768px) {
    .hero-section-container {
        .swiper-section-container {
            height: 60vh;

            .swiper-container {

                .swiper-button-next,
                .swiper-button-prev {
                    &::after {
                        font-size: 20px;
                    }
                }
            }
        }

        .hero-section-content {
            margin-top: 40px;
            padding: 0 20px;

            .hero-section-text {
                font-size: 18px;
                line-height: 28px;
            }
        }
    }
}