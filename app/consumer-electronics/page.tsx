import "./ConsumerElectronics.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { ConsumerElectronicsImg1, ConsumerElectronicsImg2 } from "@/public";
import ProductCategoryCard from "@/components/ProductCategoryCard/ProductCategoryCard";
import { consumerElectronicsData } from "@/constant/index";



const ConsumerElectronicsPage = () => {
  return (
    <Box className="consumer-electronics-page-container">
      <Box className="consumer-electronics-page-content">
        <Box className="consumer-electronics-header-section">
          <Box className="banner-text-overlay">
            <Typography className="banner-title">
              Engineering Smart Appliances for Smarter Living.
            </Typography>
          </Box>
        </Box>

        {/* Content and Image Section */}
        <Box className="content-image-section">
          <Box className="content-text">
            {consumerElectronicsData.contentDescriptions.map((description, index) => (
              <Typography key={index} className="content-description">
                {description}
              </Typography>
            ))}
          </Box>
          <Box className="content-image">
            <Image
              src={ConsumerElectronicsImg1}
              alt="Consumer Electronics Appliances"
              className="appliances-image"
            />
          </Box>
        </Box>

        {/* Support and Delivery Section */}
        <Box className="support-delivery-section">
          <Box className="support-delivery-content">
            <Box className="support-column">
              <Typography className="section-title">
                Where we can Support !
              </Typography>
              <Box className="bullet-list">
                {consumerElectronicsData.supportServices.map((service, index) => (
                  <Typography key={index} className="bullet-item">
                    • {service}
                  </Typography>
                ))}
              </Box>
            </Box>

            <Box className="delivery-column">
              <Typography className="section-title">
                What We Deliver:
              </Typography>
              <Box className="bullet-list">
                {consumerElectronicsData.deliveryServices.map((service, index) => (
                  <Typography key={index} className="bullet-item">
                    • {service}
                  </Typography>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>

        {/* we ensure */}
        <Box className="we-ensure-section">
          <Typography className="we-ensure-title">
            We ensures that every appliance is not only connected—but
            intelligent, intuitive, and future-ready.
          </Typography>
          <Typography className="we-ensure-description">
            With Aadvik TekLabs, leverage your product engineering capabilities
            to enhance the performance, reliability, and quality of your
            products. Our expert team helps you accelerate development
            timelines, ensure compliance with global quality standards, and
            seamlessly integrate the latest in smart technology and
            energy-efficient design—empowering you to stay ahead in an
            increasingly connected world.
          </Typography>
        </Box>
        {/* Key Product Categories We Support */}
        <Box className="product-categories-section">
          <Typography className="categories-main-title">
            Key Product Categories We Support
          </Typography>
          <Box className="categories-grid">
            {consumerElectronicsData.productCategories.map((category, index) => (
              <ProductCategoryCard
                key={index}
                title={category.title}
                items={category.items}
              />
            ))}
          </Box>
        </Box>

        {/* we build next gen section */}
        <Box className="we-build-next-gen-section">
          <Box className="banner-text-overlay">
            <Typography className="banner-title">
              We build the next gen intelligent appliances smarter, faster, and
              Cleaner.
            </Typography>
          </Box>
        </Box>

        {/* icons group box */}
        <Box className="icons-group-box">
          <Image src={ConsumerElectronicsImg2} alt="Consumer Electronics" />
        </Box>
      </Box>
    </Box>
  );
};

export default ConsumerElectronicsPage;
