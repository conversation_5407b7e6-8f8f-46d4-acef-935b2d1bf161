.consumer-electronics-page-container {
  .consumer-electronics-page-content {
    .consumer-electronics-header-section {
      width: 100%;
      height: 300px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      background: #021f2e;
      margin-bottom: 80px;
      overflow: hidden;

      // Dotted pattern background
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.15) 1px,
          transparent 1px
        );
        background-size: 20px 20px;
        z-index: 1;
      }

      .banner-text-overlay {
        position: relative;
        z-index: 2;
        width: 100%;
        max-width: 90vw;

        .banner-title {
          color: #ffffff;
          font-family: Edu NSW ACT Foundation !important;
          font-weight: 400;
          font-size: 48px;
          line-height: 30px;
          letter-spacing: 0%;
          //   text-align: center;
          vertical-align: middle;
          font-style: italic;

          @media (max-width: 1024px) {
            font-size: 2.8rem;
          }

          @media (max-width: 768px) {
            font-size: 2.2rem;
          }

          @media (max-width: 480px) {
            font-size: 1.8rem;
            padding: 0 20px;
          }
        }
      }

      @media (max-width: 768px) {
        height: 300px;
        margin-bottom: 60px;
      }

      @media (max-width: 480px) {
        height: 250px;
        margin-bottom: 40px;
      }
    }

    // Support and Delivery Section
    .support-delivery-section {
      margin: 50px 93px;

      .support-delivery-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 99px;
      }

      .support-column,
      .delivery-column {
        .section-title {
          font-family: "Poppins", sans-serif;
          font-weight: 400;
          font-size: 30px;
          line-height: 100%;
          background: linear-gradient(180deg, #8cffe4 37.36%, #549989 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 30px;
        }

        .bullet-list {
          display: flex;
          flex-direction: column;
          gap: 15px;

          .bullet-item {
            font-family: "Poppins", sans-serif;
            font-weight: 400;
            font-size: 18px;
            line-height: 1.6;
            color: #ffffff;

            @media (max-width: 768px) {
              font-size: 16px;
            }
          }
        }
      }

      @media (max-width: 768px) {
        margin: 60px 40px;
      }

      @media (max-width: 480px) {
        margin: 40px 20px;
      }
    }

    // Content and Image Section
    .content-image-section {
      margin: 69px 70px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 50px;
      align-items: center;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 40px;
      }

      .content-text {
        display: flex;
        flex-direction: column;
        gap: 60px;
        background: #d9d9d933;
        padding: 29px;

        .content-description {
          font-family: Poppins !important;
          font-weight: 400;
          font-size: 20px;
          line-height: 32px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          color: #ffffff;
        }
      }

      .content-image {
        display: flex;
        justify-content: center;
        align-items: center;

        .appliances-image {
          width: 100%;
          height: auto;
          max-width: 686px;
          border-radius: 12px;
        }
      }
    }

    // we ensure
    .we-ensure-section {
      background: #2499e280;
      padding: 57px 65px 70px 70px;
      //   gap: 50px;
      .we-ensure-title {
        font-family: Poppins !important;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
      }
      .we-ensure-description {
        font-family: Poppins !important;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
        margin-top: 50px;
      }
    }
  }
}
